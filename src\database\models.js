const fs = require('fs');
const path = require('path');

// 数据文件路径
const dataPath = path.join(__dirname, 'data.json');

class Database {
    constructor() {
        this.data = null;
    }

    // 初始化数据存储
    async init() {
        try {
            if (fs.existsSync(dataPath)) {
                const fileContent = fs.readFileSync(dataPath, 'utf8');
                this.data = JSON.parse(fileContent);
            } else {
                this.data = this.getDefaultData();
                await this.save();
            }
            console.log('数据存储初始化成功');
        } catch (error) {
            console.error('数据存储初始化失败:', error);
            this.data = this.getDefaultData();
            await this.save();
        }
    }

    // 获取默认数据结构
    getDefaultData() {
        return {
            packages: [
                { id: 1, name: '￥1099 套餐', price: 1099, refine_count: 30, description: '基础套餐，包含30张精修照片' },
                { id: 2, name: '￥1599 套餐', price: 1599, refine_count: 50, description: '标准套餐，包含50张精修照片' },
                { id: 3, name: '￥2099 套餐', price: 2099, refine_count: 80, description: '豪华套餐，包含80张精修照片' }
            ],
            frames: [
                { id: 1, name: '简约白框', description: '简洁的白色相框' },
                { id: 2, name: '复古金框', description: '复古风格的金色相框' },
                { id: 3, name: '现代黑框', description: '现代简约的黑色相框' },
                { id: 4, name: '木质相框', description: '天然木质相框' }
            ],
            announcements: [
                { id: 1, title: '好评活动', content: '五星好评送精修加油包！', type: 'promotion', is_active: true },
                { id: 2, title: '续订福利', content: '老客户续订享受9折优惠', type: 'benefit', is_active: true }
            ],
            addon_services: [
                { id: 1, name: '精修加油包', price: 20, unit: '张', description: '额外的精修照片服务', is_active: true }
            ],
            settings: {
                next_id: {
                    packages: 4,
                    frames: 5,
                    announcements: 3,
                    addon_services: 2
                }
            }
        };
    }

    // 保存数据到文件
    async save() {
        try {
            fs.writeFileSync(dataPath, JSON.stringify(this.data, null, 2), 'utf8');
        } catch (error) {
            console.error('保存数据失败:', error);
            throw error;
        }
    }

    // 获取下一个ID
    getNextId(table) {
        const nextId = this.data.settings.next_id[table];
        this.data.settings.next_id[table] = nextId + 1;
        return nextId;
    }

    // 获取数据
    getData() {
        return this.data;
    }

    // 更新数据
    async updateData(newData) {
        this.data = newData;
        await this.save();
    }
}

// 数据访问对象
class PackageDAO {
    constructor(db) {
        this.db = db;
    }

    async getAll() {
        return this.db.getData().packages.sort((a, b) => a.price - b.price);
    }

    async getById(id) {
        return this.db.getData().packages.find(pkg => pkg.id === parseInt(id));
    }

    async create(packageData) {
        const data = this.db.getData();
        const newPackage = {
            id: this.db.getNextId('packages'),
            ...packageData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        data.packages.push(newPackage);
        await this.db.save();
        return { id: newPackage.id };
    }

    async update(id, packageData) {
        const data = this.db.getData();
        const index = data.packages.findIndex(pkg => pkg.id === parseInt(id));
        if (index !== -1) {
            data.packages[index] = {
                ...data.packages[index],
                ...packageData,
                updated_at: new Date().toISOString()
            };
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }

    async delete(id) {
        const data = this.db.getData();
        const index = data.packages.findIndex(pkg => pkg.id === parseInt(id));
        if (index !== -1) {
            data.packages.splice(index, 1);
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }
}

class FrameDAO {
    constructor(db) {
        this.db = db;
    }

    async getAll() {
        return this.db.getData().frames.sort((a, b) => a.name.localeCompare(b.name));
    }

    async getById(id) {
        return this.db.getData().frames.find(frame => frame.id === parseInt(id));
    }

    async create(frameData) {
        const data = this.db.getData();
        const newFrame = {
            id: this.db.getNextId('frames'),
            ...frameData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        data.frames.push(newFrame);
        await this.db.save();
        return { id: newFrame.id };
    }

    async update(id, frameData) {
        const data = this.db.getData();
        const index = data.frames.findIndex(frame => frame.id === parseInt(id));
        if (index !== -1) {
            data.frames[index] = {
                ...data.frames[index],
                ...frameData,
                updated_at: new Date().toISOString()
            };
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }

    async delete(id) {
        const data = this.db.getData();
        const index = data.frames.findIndex(frame => frame.id === parseInt(id));
        if (index !== -1) {
            data.frames.splice(index, 1);
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }
}

class AnnouncementDAO {
    constructor(db) {
        this.db = db;
    }

    async getAll() {
        return this.db.getData().announcements
            .filter(ann => ann.is_active)
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }

    async getById(id) {
        return this.db.getData().announcements.find(ann => ann.id === parseInt(id));
    }

    async create(announcementData) {
        const data = this.db.getData();
        const newAnnouncement = {
            id: this.db.getNextId('announcements'),
            ...announcementData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        data.announcements.push(newAnnouncement);
        await this.db.save();
        return { id: newAnnouncement.id };
    }

    async update(id, announcementData) {
        const data = this.db.getData();
        const index = data.announcements.findIndex(ann => ann.id === parseInt(id));
        if (index !== -1) {
            data.announcements[index] = {
                ...data.announcements[index],
                ...announcementData,
                updated_at: new Date().toISOString()
            };
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }

    async delete(id) {
        const data = this.db.getData();
        const index = data.announcements.findIndex(ann => ann.id === parseInt(id));
        if (index !== -1) {
            data.announcements.splice(index, 1);
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }
}

class AddonServiceDAO {
    constructor(db) {
        this.db = db;
    }

    async getAll() {
        return this.db.getData().addon_services
            .filter(service => service.is_active)
            .sort((a, b) => a.name.localeCompare(b.name));
    }

    async getById(id) {
        return this.db.getData().addon_services.find(service => service.id === parseInt(id));
    }

    async create(serviceData) {
        const data = this.db.getData();
        const newService = {
            id: this.db.getNextId('addon_services'),
            ...serviceData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        data.addon_services.push(newService);
        await this.db.save();
        return { id: newService.id };
    }

    async update(id, serviceData) {
        const data = this.db.getData();
        const index = data.addon_services.findIndex(service => service.id === parseInt(id));
        if (index !== -1) {
            data.addon_services[index] = {
                ...data.addon_services[index],
                ...serviceData,
                updated_at: new Date().toISOString()
            };
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }

    async delete(id) {
        const data = this.db.getData();
        const index = data.addon_services.findIndex(service => service.id === parseInt(id));
        if (index !== -1) {
            data.addon_services.splice(index, 1);
            await this.db.save();
            return { changes: 1 };
        }
        return { changes: 0 };
    }
}

module.exports = {
    Database,
    PackageDAO,
    FrameDAO,
    AnnouncementDAO,
    AddonServiceDAO
};
