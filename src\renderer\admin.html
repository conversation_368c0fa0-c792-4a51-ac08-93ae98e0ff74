<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 照片精选系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="admin-app">
        <!-- 顶部导航 -->
        <div class="admin-header">
            <div class="admin-header-content">
                <h1>管理后台</h1>
                <div class="admin-nav">
                    <button class="nav-btn active" data-tab="packages">套餐管理</button>
                    <button class="nav-btn" data-tab="frames">相框管理</button>
                    <button class="nav-btn" data-tab="announcements">公告管理</button>
                    <button class="nav-btn" data-tab="addon-services">增值服务</button>
                </div>
                <button id="back-to-main" class="back-button">返回主界面</button>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="admin-content">
            <!-- 套餐管理 -->
            <div id="packages-tab" class="tab-content active">
                <div class="tab-header">
                    <h2>套餐管理</h2>
                    <button id="add-package-btn" class="add-button">添加套餐</button>
                </div>
                <div class="tab-description">
                    <p>管理客户可选择的套餐，包括价格、精修张数等信息。套餐将在主界面的"产品选择"区域显示。</p>
                </div>
                <div class="content-grid">
                    <div id="packages-list" class="items-list">
                        <!-- 套餐列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 相框管理 -->
            <div id="frames-tab" class="tab-content">
                <div class="tab-header">
                    <h2>相框管理</h2>
                    <button id="add-frame-btn" class="add-button">添加相框</button>
                </div>
                <div class="tab-description">
                    <p>管理客户可选择的相框样式。客户选择精修照片后，可以为每张照片指定相框，相框信息会添加到文件名中。</p>
                </div>
                <div class="content-grid">
                    <div id="frames-list" class="items-list">
                        <!-- 相框列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 公告管理 -->
            <div id="announcements-tab" class="tab-content">
                <div class="tab-header">
                    <h2>公告管理</h2>
                    <button id="add-announcement-btn" class="add-button">添加公告</button>
                </div>
                <div class="tab-description">
                    <p>管理在主界面左侧显示的公告信息，包括好评活动、续订福利等。可以设置公告类型和启用状态。</p>
                </div>
                <div class="content-grid">
                    <div id="announcements-list" class="items-list">
                        <!-- 公告列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 增值服务管理 -->
            <div id="addon-services-tab" class="tab-content">
                <div class="tab-header">
                    <h2>增值服务管理</h2>
                    <button id="add-addon-service-btn" class="add-button">添加增值服务</button>
                </div>
                <div class="tab-description">
                    <p>管理额外的增值服务，如精修加油包等。客户可以在主界面购买这些服务来增加精修照片数量。</p>
                </div>
                <div class="content-grid">
                    <div id="addon-services-list" class="items-list">
                        <!-- 增值服务列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑模态框 -->
        <div id="edit-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">编辑项目</h3>
                    <span class="modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="edit-form">
                        <!-- 表单内容将根据编辑类型动态生成 -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-button" id="cancel-edit">取消</button>
                    <button type="submit" form="edit-form" class="save-button">保存</button>
                </div>
            </div>
        </div>

        <!-- 确认删除模态框 -->
        <div id="confirm-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>确认删除</h3>
                    <span class="modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <p id="confirm-message">确定要删除这个项目吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-button" id="cancel-delete">取消</button>
                    <button type="button" class="delete-button" id="confirm-delete">删除</button>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="admin-loading" class="loading">
            <div class="loading-spinner"></div>
            <p>处理中...</p>
        </div>

        <!-- Toast 提示 -->
        <div id="admin-toast" class="toast"></div>
    </div>

    <script src="scripts/admin.js"></script>
</body>
</html>
