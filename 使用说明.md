# 照片选片系统使用说明

## 概述

这是一个美观、简洁、高效的网页版照片选片工具，帮助摄影师或工作室向客户交付原始照片（底片），并让客户能够方便地在线挑选需要精修的照片。

## 主要功能

### 1. 客户端操作流程

1. **选择套餐**：在左侧边栏选择合适的套餐
2. **导入照片**：点击"导入照片文件夹"按钮，选择包含照片的文件夹
3. **浏览底片**：在中间区域浏览所有导入的照片
4. **选择精修**：双击照片将其添加到右侧精修区域
5. **指定相框**：在右侧精修区选中照片，然后在左侧选择相框
6. **确认提交**：完成选择后点击"我选好了，确认提交"

### 2. 管理后台功能

点击主界面左下角的"管理设置"按钮进入管理后台，可以自定义以下内容：

#### 套餐管理
- 添加、编辑、删除套餐
- 设置套餐价格和精修张数
- 套餐描述信息

#### 相框管理
- 管理可选的相框样式
- 相框名称和描述
- 客户选择相框后会自动添加到文件名中

#### 公告管理
- 编辑活动公告和福利信息
- 设置公告类型（促销活动、福利优惠、重要通知）
- 启用/禁用公告显示

#### 增值服务管理
- 管理精修加油包等增值服务
- 设置服务价格和计价单位
- 启用/禁用服务

## 操作技巧

### 快捷键
- `数字键 1-9`：快速选择套餐
- `Ctrl+I`：导入照片文件夹
- `Ctrl+Enter`：确认提交选片
- `单击照片`：预览照片
- `双击照片`：添加/移除精修
- `ESC`：关闭预览
- `←→`：切换上下张图片
- `滚轮`：缩放图片
- `+/-`：放大/缩小图片

### 视觉提示
- 绿色勾号：已添加到精修的照片
- 蓝色边框：当前选中用于指定相框的照片
- 悬停提示：鼠标悬停在照片上会显示操作提示

## 文件导出

选片完成后，系统会：
1. 在原照片文件夹内创建"精修"文件夹
2. 复制选中的照片到精修文件夹
3. 根据相框选择重命名文件（如：`照片名_简约白框.jpg`）
4. 生成订单信息JSON文件

## 注意事项

1. **文件格式**：支持 JPG、JPEG、PNG、GIF、BMP 格式的图片
2. **文件夹结构**：导入的照片文件夹应该直接包含图片文件
3. **套餐限制**：选择照片数量不能超过套餐限制
4. **相框选择**：需要先在右侧精修区选中照片，才能指定相框
5. **浏览器模式**：在浏览器中预览时，文件操作功能仅为演示，实际使用需要Electron桌面应用

## 启动方式

### 浏览器预览（用于界面演示）
```bash
node start.js
```
然后在浏览器中打开：http://localhost:3001

### Electron桌面应用（完整功能）
```bash
npm install
npm start
```

## 技术特色

- **现代简洁**：采用卡片式设计，善用留白
- **色彩搭配**：浅灰色、白色背景，蓝色主题色
- **圆角设计**：广泛使用圆角矩形，营造亲和感
- **细微阴影**：为卡片和弹出窗口添加层次感
- **响应式交互**：悬停效果、点击反馈等

## 常见问题

**Q: 如何修改套餐信息？**
A: 进入管理后台的"套餐管理"页面，点击对应套餐的"编辑"按钮。

**Q: 相框选择不可用怎么办？**
A: 需要先在右侧精修区单击选中一张照片，然后才能在左侧选择相框。

**Q: 如何批量选择照片？**
A: 目前需要逐张双击选择，这样可以让客户更仔细地考虑每张照片。

**Q: 导出的文件在哪里？**
A: 在原照片文件夹内会自动创建"精修"文件夹，选中的照片会复制到这个文件夹中。

## 联系支持

如有问题或建议，请查看项目的 README.md 文件或联系开发团队。
