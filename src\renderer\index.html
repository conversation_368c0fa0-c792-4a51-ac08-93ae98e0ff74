<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片精选系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <!-- PhotoSwipe CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/photoswipe@5.4.2/dist/photoswipe.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 主容器 -->
        <div class="main-container">
            <!-- 左侧信息与操作栏 -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>套餐内容</h3>
                    <div class="package-content">
                        <p id="package-description">请选择套餐</p>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>产品选择</h3>
                    <div class="package-selection">
                        <button id="select-package-btn" class="select-package-button">
                            选择套餐
                        </button>
                        <div id="selected-package-info" class="selected-package-info" style="display: none;">
                            <div class="selected-package-name"></div>
                            <button id="change-package-btn" class="change-package-button">更换套餐</button>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="announcement">
                        <!-- 公告内容将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>增值服务</h3>
                    <div class="addon-services">
                        <!-- 增值服务将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <button id="confirm-btn" class="confirm-button" disabled>
                        我选好了，确认提交
                    </button>
                    <button id="help-btn" class="help-button">
                        快捷键帮助
                    </button>
                    <button id="admin-btn" class="admin-button">
                        管理设置
                    </button>
                </div>
            </div>

            <!-- 中间底片展示区 -->
            <div class="gallery-section">
                <div class="gallery-header">
                    <h2>底片 <span id="original-count">(0)</span></h2>
                    <button id="import-btn" class="import-button">
                        导入照片文件夹
                    </button>
                </div>
                <div class="gallery-container" id="original-gallery">
                    <div class="empty-state">
                        <p>点击"导入照片文件夹"开始选片</p>
                    </div>
                </div>
                <div class="gallery-tips" id="gallery-tips">
                    <div class="tip-item">
                        <span class="tip-icon">👆</span>
                        <span class="tip-text">单击预览照片</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">👆👆</span>
                        <span class="tip-text">双击添加到精修</span>
                    </div>
                </div>
            </div>

            <!-- 右侧精修选择区 -->
            <div class="selected-section">
                <div class="selected-header">
                    <h2>精修 <span id="selected-count">(0/0)</span></h2>
                    <div id="selected-image-name" class="selected-image-name"></div>
                </div>
                <div class="selected-container" id="selected-gallery">
                    <div class="empty-state">
                        <p>双击底片区的照片添加到精修</p>
                    </div>
                </div>
                <div class="selected-tips" id="selected-tips">
                    <div class="tip-item">
                        <span class="tip-icon">👆</span>
                        <span class="tip-text">右键选择相框</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">👆👆</span>
                        <span class="tip-text">双击移除精修</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- PhotoSwipe 容器 -->
        <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="pswp__bg"></div>
            <div class="pswp__scroll-wrap">
                <div class="pswp__container">
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                </div>
                <div class="pswp__ui pswp__ui--hidden">
                    <div class="pswp__top-bar">
                        <div class="pswp__counter"></div>
                        <button class="pswp__button pswp__button--close" title="关闭 (Esc)"></button>
                        <button class="pswp__button pswp__button--share" title="分享"></button>
                        <button class="pswp__button pswp__button--fs" title="全屏"></button>
                        <button class="pswp__button pswp__button--zoom" title="缩放"></button>
                        <div class="pswp__preloader">
                            <div class="pswp__preloader__icn">
                                <div class="pswp__preloader__cut">
                                    <div class="pswp__preloader__donut"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                        <div class="pswp__share-tooltip"></div>
                    </div>
                    <button class="pswp__button pswp__button--arrow--left" title="上一张 (方向键左)"></button>
                    <button class="pswp__button pswp__button--arrow--right" title="下一张 (方向键右)"></button>
                    <div class="pswp__caption">
                        <div class="pswp__caption__center"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 套餐选择模态框 -->
        <div id="package-modal" class="package-modal">
            <div class="package-modal-content">
                <div class="package-modal-header">
                    <h3>选择套餐</h3>
                    <span class="package-modal-close">&times;</span>
                </div>
                <div class="package-modal-body">
                    <div id="package-options" class="package-options">
                        <!-- 套餐选项将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 相框选择右键菜单 -->
        <div id="frame-context-menu" class="context-menu">
            <div class="context-menu-header">选择相框</div>
            <div id="frame-menu-options" class="context-menu-options">
                <!-- 相框选项将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 管理设置密码验证模态框 -->
        <div id="admin-password-modal" class="admin-password-modal">
            <div class="admin-password-content">
                <div class="admin-password-header">
                    <h3>管理员验证</h3>
                    <span class="admin-password-close">&times;</span>
                </div>
                <div class="admin-password-body">
                    <div class="form-group">
                        <label>请输入管理密码：</label>
                        <input type="password" id="admin-password-input" class="form-input" placeholder="请输入密码">
                    </div>
                    <div class="admin-password-actions">
                        <button id="admin-password-cancel" class="cancel-button">取消</button>
                        <button id="admin-password-confirm" class="confirm-button">确认</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- PhotoSwipe JS -->
    <script src="https://cdn.jsdelivr.net/npm/photoswipe@5.4.2/dist/photoswipe.umd.min.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
