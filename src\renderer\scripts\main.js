// 检查是否在Electron环境中
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;
const ipcRenderer = isElectron ? require('electron').ipcRenderer : null;

// 应用状态
let appState = {
    originalImages: [],
    selectedImages: [],
    currentPackage: null,
    selectedImageForFrame: null,
    sourceFolderPath: null,

    addonServices: {},
    availablePackages: [],
    availableFrames: []
};

// DOM 元素
const elements = {
    importBtn: document.getElementById('import-btn'),
    originalGallery: document.getElementById('original-gallery'),
    selectedGallery: document.getElementById('selected-gallery'),
    originalCount: document.getElementById('original-count'),
    selectedCount: document.getElementById('selected-count'),
    packageDescription: document.getElementById('package-description'),
    confirmBtn: document.getElementById('confirm-btn'),
    helpBtn: document.getElementById('help-btn'),
    adminBtn: document.getElementById('admin-btn'),
    selectedImageName: document.getElementById('selected-image-name'),
    galleryTips: document.getElementById('gallery-tips'),
    selectedTips: document.getElementById('selected-tips'),

    loading: document.getElementById('loading'),

    // 新增的元素
    selectPackageBtn: document.getElementById('select-package-btn'),
    changePackageBtn: document.getElementById('change-package-btn'),
    selectedPackageInfo: document.getElementById('selected-package-info'),
    packageModal: document.getElementById('package-modal'),
    packageModalClose: document.querySelector('.package-modal-close'),
    packageOptions: document.getElementById('package-options'),
    frameContextMenu: document.getElementById('frame-context-menu'),
    frameMenuOptions: document.getElementById('frame-menu-options'),
    adminPasswordModal: document.getElementById('admin-password-modal'),
    adminPasswordClose: document.querySelector('.admin-password-close'),
    adminPasswordInput: document.getElementById('admin-password-input'),
    adminPasswordCancel: document.getElementById('admin-password-cancel'),
    adminPasswordConfirm: document.getElementById('admin-password-confirm')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    await loadInitialData();
    initializeEventListeners();
    updatePackageDisplay(); // 初始化套餐显示状态
    updateUI();
});

// 加载初始数据
async function loadInitialData() {
    try {
        if (ipcRenderer) {
            // Electron环境，从主进程加载数据
            const packages = await ipcRenderer.invoke('get-packages');
            renderPackages(packages);

            const frames = await ipcRenderer.invoke('get-frames');
            renderFrames(frames);

            const announcements = await ipcRenderer.invoke('get-announcements');
            renderAnnouncements(announcements);

            const addonServices = await ipcRenderer.invoke('get-addon-services');
            renderAddonServices(addonServices);
        } else {
            // 浏览器环境，使用模拟数据
            const mockData = getMockData();
            renderPackages(mockData.packages);
            renderFrames(mockData.frames);
            renderAnnouncements(mockData.announcements);
            renderAddonServices(mockData.addon_services);
        }
    } catch (error) {
        console.error('加载初始数据失败:', error);
    }
}

// 获取模拟数据（用于浏览器预览）
function getMockData() {
    return {
        packages: [
            { id: 1, name: '￥1099 套餐', price: 1099, refine_count: 30, description: '基础套餐，包含30张精修照片' },
            { id: 2, name: '￥1599 套餐', price: 1599, refine_count: 50, description: '标准套餐，包含50张精修照片' },
            { id: 3, name: '￥2099 套餐', price: 2099, refine_count: 80, description: '豪华套餐，包含80张精修照片' }
        ],
        frames: [
            { id: 1, name: '简约白框', description: '简洁的白色相框' },
            { id: 2, name: '复古金框', description: '复古风格的金色相框' },
            { id: 3, name: '现代黑框', description: '现代简约的黑色相框' },
            { id: 4, name: '木质相框', description: '天然木质相框' }
        ],
        announcements: [
            { id: 1, title: '好评活动', content: '五星好评送精修加油包！', type: 'promotion', is_active: true },
            { id: 2, title: '续订福利', content: '老客户续订享受9折优惠', type: 'benefit', is_active: true }
        ],
        addon_services: [
            { id: 1, name: '精修加油包', price: 20, unit: '张', description: '额外的精修照片服务', is_active: true }
        ]
    };
}

// 渲染套餐选项
function renderPackages(packages) {
    // 存储套餐数据供后续使用
    appState.availablePackages = packages;

    // 渲染模态框中的套餐选项
    const packageOptions = elements.packageOptions;
    packageOptions.innerHTML = '';

    packages.forEach((pkg, index) => {
        const div = document.createElement('div');
        div.className = 'package-option-card';
        div.innerHTML = `
            <div class="package-card-header">
                <h4>${pkg.name}</h4>
                <div class="package-price">￥${pkg.price}</div>
            </div>
            <div class="package-card-content">
                <p class="package-description">${pkg.description}</p>
                <div class="package-details">
                    <span class="package-count">${pkg.refine_count}张精修</span>
                </div>
            </div>
            <button class="select-package-card-btn" data-package-id="${pkg.id}">选择此套餐</button>
        `;
        packageOptions.appendChild(div);
    });

    // 绑定套餐选择事件
    document.querySelectorAll('.select-package-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const packageId = parseInt(e.target.dataset.packageId);
            selectPackage(packageId);
        });
    });
}

// 渲染相框选项
function renderFrames(frames) {
    // 存储相框数据供右键菜单使用
    appState.availableFrames = frames;

    // 渲染右键菜单中的相框选项
    const frameMenuOptions = elements.frameMenuOptions;
    frameMenuOptions.innerHTML = '';

    // 添加"无相框"选项
    const noFrameOption = document.createElement('div');
    noFrameOption.className = 'context-menu-item';
    noFrameOption.textContent = '无相框';
    noFrameOption.addEventListener('click', () => {
        setImageFrame(null);
        hideFrameContextMenu();
    });
    frameMenuOptions.appendChild(noFrameOption);

    // 添加相框选项
    frames.forEach(frame => {
        const option = document.createElement('div');
        option.className = 'context-menu-item';
        option.textContent = frame.name;
        option.addEventListener('click', () => {
            setImageFrame(frame.name);
            hideFrameContextMenu();
        });
        frameMenuOptions.appendChild(option);
    });
}

// 渲染公告
function renderAnnouncements(announcements) {
    const announcementContainer = document.querySelector('.announcement');
    announcementContainer.innerHTML = '';

    announcements.forEach(announcement => {
        const div = document.createElement('div');
        div.className = 'announcement-item';
        div.innerHTML = `
            <h4>${announcement.title}</h4>
            <p>${announcement.content}</p>
        `;
        announcementContainer.appendChild(div);
    });
}

// 渲染增值服务
function renderAddonServices(services) {
    const addonContainer = document.querySelector('.addon-services');
    addonContainer.innerHTML = '';

    services.forEach(service => {
        const div = document.createElement('div');
        div.className = 'addon-item';
        div.innerHTML = `
            <label>${service.name}：</label>
            <input type="number" id="addon-${service.id}" min="0" value="0" class="addon-input" data-price="${service.price}" data-service-id="${service.id}">
            <span>${service.unit} (￥${service.price}/${service.unit})</span>
        `;
        addonContainer.appendChild(div);

        // 绑定变更事件
        const input = div.querySelector(`#addon-${service.id}`);
        input.addEventListener('change', updateAddonServices);
        input.addEventListener('input', updateAddonServices);
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 导入照片按钮
    elements.importBtn.addEventListener('click', importPhotos);

    // 套餐选择相关
    elements.selectPackageBtn.addEventListener('click', showPackageModal);
    elements.changePackageBtn.addEventListener('click', showPackageModal);
    elements.packageModalClose.addEventListener('click', hidePackageModal);

    // 确认提交按钮
    elements.confirmBtn.addEventListener('click', handleConfirmSubmit);

    // 帮助按钮
    elements.helpBtn.addEventListener('click', showHelp);

    // 管理后台按钮
    elements.adminBtn.addEventListener('click', showAdminPasswordModal);

    // 管理密码验证相关
    elements.adminPasswordClose.addEventListener('click', hideAdminPasswordModal);
    elements.adminPasswordCancel.addEventListener('click', hideAdminPasswordModal);
    elements.adminPasswordConfirm.addEventListener('click', verifyAdminPassword);
    elements.adminPasswordInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            verifyAdminPassword();
        }
    });

    // 键盘事件
    document.addEventListener('keydown', handleKeyDown);

    // 点击套餐模态框背景关闭
    elements.packageModal.addEventListener('click', (e) => {
        if (e.target === elements.packageModal) {
            hidePackageModal();
        }
    });

    // 点击管理密码模态框背景关闭
    elements.adminPasswordModal.addEventListener('click', (e) => {
        if (e.target === elements.adminPasswordModal) {
            hideAdminPasswordModal();
        }
    });

    // 全局点击事件，用于隐藏右键菜单
    document.addEventListener('click', (e) => {
        if (!elements.frameContextMenu.contains(e.target)) {
            hideFrameContextMenu();
        }
    });
}

// 导入照片
async function importPhotos() {
    // 检查是否已选择套餐
    if (!appState.currentPackage) {
        alert('请先选择套餐再导入照片');
        showPackageModal();
        return;
    }

    if (!ipcRenderer) {
        // 浏览器预览模式，使用模拟数据
        loadMockImages();
        return;
    }

    try {
        showLoading(true);

        const folderPath = await ipcRenderer.invoke('select-folder');
        if (!folderPath) {
            showLoading(false);
            return;
        }

        appState.sourceFolderPath = folderPath;
        const images = await ipcRenderer.invoke('read-images', folderPath);

        appState.originalImages = images.map(img => ({
            name: img.name,
            path: img.path,
            id: generateId()
        }));

        renderOriginalGallery();
        updateUI();

    } catch (error) {
        console.error('导入照片失败:', error);
        alert('导入照片失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 加载模拟图片（用于浏览器预览）
function loadMockImages() {
    // 检查是否已选择套餐
    if (!appState.currentPackage) {
        alert('请先选择套餐再导入照片');
        showPackageModal();
        return;
    }

    showLoading(true);

    try {
        // 模拟一些图片数据
        const mockImages = [];
        for (let i = 1; i <= 24; i++) {
            mockImages.push({
                name: `IMG_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/300/200?random=${i}`, // 使用Lorem Picsum提供的随机图片
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/mock/photos'; // 模拟路径

        setTimeout(() => {
            renderOriginalGallery();
            updateUI();
            showLoading(false);
            showToast(`已加载 ${mockImages.length} 张模拟照片`, 'info');
        }, 1000); // 模拟加载时间

    } catch (error) {
        console.error('加载模拟图片失败:', error);
        showLoading(false);
        showToast('加载图片失败，请重试', 'error');
    }
}

// 渲染原始照片画廊
function renderOriginalGallery() {
    if (appState.originalImages.length === 0) {
        elements.originalGallery.innerHTML = '<div class="empty-state"><p>点击"导入照片文件夹"开始选片</p></div>';
        elements.galleryTips.style.display = 'none';
        return;
    }

    elements.originalGallery.innerHTML = '';
    elements.galleryTips.style.display = 'flex';

    appState.originalImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'original');

        // 检查是否已在精修区
        const isInSelected = appState.selectedImages.some(selected => selected.id === image.id);
        if (isInSelected) {
            thumbnail.classList.add('in-selected');
        }

        elements.originalGallery.appendChild(thumbnail);
    });
}

// 渲染选中照片画廊
function renderSelectedGallery() {
    if (appState.selectedImages.length === 0) {
        elements.selectedGallery.innerHTML = '<div class="empty-state"><p>双击底片区的照片添加到精修</p></div>';
        elements.selectedTips.style.display = 'none';
        return;
    }

    elements.selectedGallery.innerHTML = '';
    elements.selectedTips.style.display = 'flex';

    appState.selectedImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'selected');
        elements.selectedGallery.appendChild(thumbnail);
    });
}

// 创建图片缩略图
function createImageThumbnail(image, index, type) {
    const div = document.createElement('div');
    div.className = 'image-thumbnail';
    div.dataset.imageId = image.id;
    div.dataset.type = type;

    const img = document.createElement('img');
    // 使用懒加载和占位符
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=';
    img.dataset.src = ipcRenderer ? `file://${image.path}` : image.path;
    img.alt = image.name;
    img.loading = 'lazy';

    // 实现懒加载 - 优化大图片处理
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                loadImageWithOptimization(img, img.dataset.src);
                observer.unobserve(img);
            }
        });
    }, { threshold: 0.1, rootMargin: '50px' }); // 增加rootMargin提前加载

    observer.observe(img);
    img.style.opacity = '0.5';
    img.style.transition = 'opacity 0.3s ease';

    const info = document.createElement('div');
    info.className = 'image-info';
    info.innerHTML = `
        <div class="image-name">${truncateFileName(image.name, 15)}</div>
        ${image.frame ? `<div class="frame-info">相框: ${image.frame}</div>` : ''}
    `;

    div.appendChild(img);
    div.appendChild(info);

    // 防抖处理点击事件
    let clickTimeout;
    div.addEventListener('click', () => {
        clearTimeout(clickTimeout);
        clickTimeout = setTimeout(() => {
            if (type === 'selected') {
                selectImageForFrame(image);
            }
            showLightbox(image, type);
        }, 200);
    });

    // 双击选择/取消
    div.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        clearTimeout(clickTimeout);
        if (type === 'original') {
            addToSelected(image);
        } else {
            removeFromSelected(image);
        }
    });

    // 右键菜单（仅对精修区的图片）
    if (type === 'selected') {
        div.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            appState.selectedImageForFrame = image;
            showFrameContextMenu(e.clientX, e.clientY);
        });
    }

    return div;
}

// 添加到精修
function addToSelected(image) {
    // 检查是否已经选中
    if (appState.selectedImages.find(img => img.id === image.id)) {
        return;
    }

    // 检查套餐限制
    const maxCount = getCurrentPackageLimit();
    if (maxCount && appState.selectedImages.length >= maxCount) {
        alert(`当前套餐最多只能选择 ${maxCount} 张照片`);
        return;
    }

    appState.selectedImages.push({
        ...image,
        frame: null
    });

    // 重新渲染两个画廊以更新视觉状态
    renderSelectedGallery();
    renderOriginalGallery();
    updateUI();

    // 显示成功提示
    showToast(`已添加 ${image.name} 到精修区`);
}

// 从精修中移除
function removeFromSelected(image) {
    appState.selectedImages = appState.selectedImages.filter(img => img.id !== image.id);

    // 如果移除的是当前选中的图片，清除相框选择
    if (appState.selectedImageForFrame && appState.selectedImageForFrame.id === image.id) {
        appState.selectedImageForFrame = null;
        updateSelectedImageName(null); // 清除图片名字显示
    }

    // 重新渲染两个画廊以更新视觉状态
    renderSelectedGallery();
    renderOriginalGallery();
    updateUI();

    // 显示成功提示
    showToast(`已从精修区移除 ${image.name}`);
}

// 选择图片用于指定相框（保留用于单击预览）
function selectImageForFrame(image) {
    // 更新图片名字显示
    updateSelectedImageName(image.name);
}

// 这个函数已被新的套餐选择逻辑替代，保留空函数以防其他地方调用
function handlePackageChange(e) {
    // 已被新的套餐选择逻辑替代
}

// 更新增值服务
function updateAddonServices() {
    const addonInputs = document.querySelectorAll('.addon-input');
    appState.addonServices = {};

    addonInputs.forEach(input => {
        const serviceId = input.dataset.serviceId;
        const quantity = parseInt(input.value) || 0;
        const price = parseInt(input.dataset.price) || 0;

        if (quantity > 0) {
            appState.addonServices[serviceId] = {
                quantity: quantity,
                price: price,
                total: quantity * price
            };
        }
    });

    updatePriceDisplay();
}

// 更新价格显示
function updatePriceDisplay() {
    let totalPrice = 0;

    // 套餐价格
    if (appState.currentPackage) {
        totalPrice += appState.currentPackage.price;
    }

    // 增值服务价格
    Object.values(appState.addonServices).forEach(service => {
        totalPrice += service.total;
    });

    // 更新套餐内容显示
    if (appState.currentPackage) {
        let description = `（￥${appState.currentPackage.price}套餐，${appState.currentPackage.count}张精修）`;

        const addonTotal = totalPrice - appState.currentPackage.price;
        if (addonTotal > 0) {
            description += `\n增值服务：￥${addonTotal}`;
            description += `\n总计：￥${totalPrice}`;
        }

        elements.packageDescription.innerHTML = description.replace(/\n/g, '<br>');
    }
}

// 这个函数已被右键菜单逻辑替代，保留空函数以防其他地方调用
function handleFrameChange(e) {
    // 已被右键菜单逻辑替代
}

// 处理确认提交
async function handleConfirmSubmit() {
    if (!appState.sourceFolderPath || appState.selectedImages.length === 0) {
        alert('请先导入照片并选择要精修的照片');
        return;
    }

    if (!appState.currentPackage) {
        alert('请选择套餐');
        return;
    }

    try {
        showLoading(true);

        // 准备导出数据
        const selectedImages = appState.selectedImages.map(img => ({
            originalName: img.name,
            finalName: img.frame ? `${getFileNameWithoutExt(img.name)}_${img.frame}.${getFileExtension(img.name)}` : img.name
        }));

        if (ipcRenderer) {
            // 准备订单信息
            const orderInfo = {
                package: appState.currentPackage,
                addonServices: appState.addonServices,
                totalImages: appState.selectedImages.length,
                totalPrice: calculateTotalPrice()
            };

            // Electron环境
            const result = await ipcRenderer.invoke('export-selected-images', {
                sourceFolderPath: appState.sourceFolderPath,
                selectedImages,
                orderInfo
            });

            if (result.success) {
                alert(`选片完成！已在原文件夹内生成"精修"文件夹。\n路径：${result.path}`);
            } else {
                alert(`导出失败：${result.error}`);
            }
        } else {
            // 浏览器环境，显示选择结果
            let resultText = '选片结果预览：\n\n';
            resultText += `套餐：${appState.currentPackage.price}元套餐 (${appState.currentPackage.count}张精修)\n`;
            resultText += `已选择：${appState.selectedImages.length}张照片\n`;

            // 增值服务信息
            const addonServices = Object.values(appState.addonServices);
            if (addonServices.length > 0) {
                resultText += '\n增值服务：\n';
                addonServices.forEach(service => {
                    resultText += `- 精修加油包：${service.quantity}张 (￥${service.total})\n`;
                });
            }

            // 总价计算
            let totalPrice = appState.currentPackage.price;
            addonServices.forEach(service => totalPrice += service.total);
            resultText += `\n总价：￥${totalPrice}\n\n`;

            resultText += '选中的照片：\n';
            selectedImages.forEach((img, index) => {
                resultText += `${index + 1}. ${img.finalName}\n`;
            });

            resultText += '\n注意：实际文件导出功能需要在Electron桌面应用中使用。';
            alert(resultText);
        }

    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 优化图片加载 - 处理大图片
function loadImageWithOptimization(imgElement, src) {
    const img = new Image();

    img.onload = () => {
        // 检查图片大小，如果太大则进行优化
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 计算缩略图尺寸（最大200px）
        const maxSize = 200;
        let { width, height } = img;

        if (width > height) {
            if (width > maxSize) {
                height = (height * maxSize) / width;
                width = maxSize;
            }
        } else {
            if (height > maxSize) {
                width = (width * maxSize) / height;
                height = maxSize;
            }
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制缩略图
        ctx.drawImage(img, 0, 0, width, height);

        // 使用优化后的图片
        imgElement.src = canvas.toDataURL('image/jpeg', 0.8);
        imgElement.style.opacity = '1';

        // 清理canvas
        canvas.remove();
    };

    img.onerror = () => {
        // 如果优化失败，直接使用原图
        imgElement.src = src;
        imgElement.style.opacity = '1';
    };

    img.src = src;
}

// 显示PhotoSwipe图片预览
function showLightbox(image, type) {
    const images = type === 'original' ? appState.originalImages : appState.selectedImages;
    const currentIndex = images.findIndex(img => img.id === image.id);

    // 准备PhotoSwipe数据源
    const items = images.map(img => ({
        src: ipcRenderer ? `file://${img.path}` : img.path,
        width: 1920, // 默认宽度，PhotoSwipe会自动调整
        height: 1080, // 默认高度，PhotoSwipe会自动调整
        title: img.name + (img.frame ? ` (相框: ${img.frame})` : '')
    }));

    // 初始化PhotoSwipe
    const lightbox = new PhotoSwipe({
        dataSource: items,
        index: currentIndex,
        bgOpacity: 0.9,
        showHideAnimationType: 'zoom',

        // 自定义选项 - 优化大图片处理
        closeOnVerticalDrag: true,
        mouseMovePan: true,
        loop: false,
        wheelToZoom: true,

        // 缩放设置
        initialZoomLevel: 'fit',
        secondaryZoomLevel: 1.5,
        maxZoomLevel: 3,

        // 工具栏按钮
        toolbar: [
            'zoom',
            'close'
        ],

        // 预加载设置 - 为大图片优化
        preloadFirstSlide: true,
        preload: [1, 2], // 减少预加载数量以节省内存

        // 图片加载错误处理
        errorMsg: '图片加载失败',

        // 性能优化
        spacing: 0.1,
        allowPanToNext: false,

        // 自定义UI文本
        closeTitle: '关闭 (Esc)',
        zoomTitle: '缩放',
        arrowPrevTitle: '上一张',
        arrowNextTitle: '下一张'
    });

    // 添加图片加载事件监听
    lightbox.on('imageLoadComplete', (e) => {
        console.log('图片加载完成:', e.slide.data.src);
    });

    lightbox.on('loadComplete', (e) => {
        console.log('PhotoSwipe 加载完成');
    });

    lightbox.init();
}



// 处理键盘事件
function handleKeyDown(e) {

    // 全局快捷键
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'i':
                e.preventDefault();
                importPhotos();
                break;
            case 'Enter':
                e.preventDefault();
                if (!elements.confirmBtn.disabled) {
                    handleConfirmSubmit();
                }
                break;
        }
    }

    // 数字键选择套餐（如果套餐模态框打开）
    if (e.key >= '1' && e.key <= '9' && elements.packageModal.style.display === 'block') {
        const index = parseInt(e.key) - 1;
        if (appState.availablePackages[index]) {
            selectPackage(appState.availablePackages[index].id);
        }
    }
}

// 更新UI状态
function updateUI() {
    // 更新计数器
    elements.originalCount.textContent = `(${appState.originalImages.length})`;

    const maxCount = getCurrentPackageLimit();
    const selectedCount = appState.selectedImages.length;

    if (maxCount) {
        elements.selectedCount.textContent = `(${selectedCount}/${maxCount})`;
        // 根据选择状态改变颜色
        if (selectedCount > maxCount) {
            elements.selectedCount.style.color = '#dc3545'; // 红色：超出限制
        } else if (selectedCount === maxCount) {
            elements.selectedCount.style.color = '#28a745'; // 绿色：刚好满足
        } else {
            elements.selectedCount.style.color = '#333'; // 默认颜色
        }
    } else {
        elements.selectedCount.textContent = `(${selectedCount}/0)`;
        elements.selectedCount.style.color = '#6c757d'; // 灰色：未选择套餐
    }

    // 更新确认按钮状态
    const canConfirm = appState.currentPackage &&
                      appState.selectedImages.length > 0 &&
                      appState.selectedImages.length <= getCurrentPackageLimit() &&
                      appState.sourceFolderPath;

    elements.confirmBtn.disabled = !canConfirm;

    // 更新按钮文本提示
    if (!appState.currentPackage) {
        elements.confirmBtn.textContent = '请先选择套餐';
    } else if (appState.selectedImages.length === 0) {
        elements.confirmBtn.textContent = '请选择要精修的照片';
    } else if (appState.selectedImages.length > getCurrentPackageLimit()) {
        elements.confirmBtn.textContent = `超出套餐限制 (${appState.selectedImages.length}/${getCurrentPackageLimit()})`;
    } else if (!appState.sourceFolderPath) {
        elements.confirmBtn.textContent = '请先导入照片';
    } else {
        elements.confirmBtn.textContent = '我选好了，确认提交';
    }
}

// 获取当前套餐限制
function getCurrentPackageLimit() {
    return appState.currentPackage ? appState.currentPackage.count : 0;
}

// 计算总价
function calculateTotalPrice() {
    let totalPrice = 0;

    // 套餐价格
    if (appState.currentPackage) {
        totalPrice += appState.currentPackage.price;
    }

    // 增值服务价格
    Object.values(appState.addonServices).forEach(service => {
        totalPrice += service.total;
    });

    return totalPrice;
}

// 显示/隐藏加载状态
function showLoading(show) {
    elements.loading.style.display = show ? 'flex' : 'none';
}

// 工具函数
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function getFileNameWithoutExt(filename) {
    return filename.substring(0, filename.lastIndexOf('.'));
}

function getFileExtension(filename) {
    return filename.substring(filename.lastIndexOf('.') + 1);
}

// 显示toast提示
function showToast(message, type = 'success') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}



// 截断文件名
function truncateFileName(fileName, maxLength) {
    if (fileName.length <= maxLength) return fileName;

    const ext = fileName.substring(fileName.lastIndexOf('.'));
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = name.substring(0, maxLength - ext.length - 3) + '...';

    return truncatedName + ext;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 性能监控
function measurePerformance(name, func) {
    return async function(...args) {
        const start = performance.now();
        const result = await func.apply(this, args);
        const end = performance.now();
        console.log(`${name} 执行时间: ${(end - start).toFixed(2)}ms`);
        return result;
    };
}

// 内存清理
function cleanupResources() {
    // 清理未使用的图片引用
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (!img.parentNode) {
            img.src = '';
        }
    });

    // 强制垃圾回收（如果可用）
    if (window.gc) {
        window.gc();
    }
}

// 显示帮助信息
function showHelp() {
    const helpText = `
照片精选系统 - 快捷键帮助

基本操作：
• 单击照片：预览照片
• 双击照片：添加/移除精修
• 右键照片：选择相框（精修区）

图片预览：
• ESC：关闭预览
• ←→：切换上下张
• 滚轮：缩放图片
• 双击：适应屏幕/放大
• 拖拽：移动图片
• 双指手势：缩放（移动端）

全局快捷键：
• Ctrl+I：导入照片
• Ctrl+Enter：确认提交

操作流程：
1. 点击"选择套餐"按钮选择套餐
2. 导入照片文件夹
3. 双击选择要精修的照片
4. 右键精修区照片选择相框（可选）
5. 确认提交

注意：必须先选择套餐才能导入照片。
浏览器预览模式仅供界面演示，
完整功能需要在Electron桌面应用中使用。
    `.trim();

    alert(helpText);
}

// 显示套餐选择模态框
function showPackageModal() {
    elements.packageModal.style.display = 'block';
}

// 隐藏套餐选择模态框
function hidePackageModal() {
    elements.packageModal.style.display = 'none';
}

// 选择套餐
function selectPackage(packageId) {
    const pkg = appState.availablePackages.find(p => p.id === packageId);
    if (!pkg) return;

    // 检查是否需要在选片之前选择套餐
    if (appState.originalImages.length > 0) {
        if (!confirm('更换套餐将清空已选择的照片，是否继续？')) {
            return;
        }
        // 清空已选择的照片
        appState.selectedImages = [];
        appState.selectedImageForFrame = null;
        renderSelectedGallery();
        renderOriginalGallery();
    }

    appState.currentPackage = {
        id: pkg.id,
        price: pkg.price,
        count: pkg.refine_count,
        name: pkg.name
    };

    // 更新UI显示
    updatePackageDisplay();
    updateUI();
    hidePackageModal();

    showToast(`已选择 ${pkg.name}`);
}

// 更新套餐显示
function updatePackageDisplay() {
    if (appState.currentPackage) {
        // 隐藏选择按钮，显示已选择的套餐信息
        elements.selectPackageBtn.style.display = 'none';
        elements.selectedPackageInfo.style.display = 'block';

        const selectedPackageName = elements.selectedPackageInfo.querySelector('.selected-package-name');
        selectedPackageName.textContent = appState.currentPackage.name;

        // 更新套餐内容描述
        elements.packageDescription.innerHTML = `${appState.currentPackage.name}<br>包含${appState.currentPackage.count}张精修照片`;
    } else {
        // 显示选择按钮，隐藏已选择的套餐信息
        elements.selectPackageBtn.style.display = 'block';
        elements.selectedPackageInfo.style.display = 'none';
        elements.packageDescription.textContent = '请选择套餐';
    }
}

// 显示相框右键菜单
function showFrameContextMenu(x, y) {
    const menu = elements.frameContextMenu;
    menu.style.display = 'block';

    // 调整菜单位置，确保不超出屏幕边界
    const menuRect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = x;
    let top = y;

    if (left + menuRect.width > viewportWidth) {
        left = viewportWidth - menuRect.width - 10;
    }

    if (top + menuRect.height > viewportHeight) {
        top = viewportHeight - menuRect.height - 10;
    }

    menu.style.left = left + 'px';
    menu.style.top = top + 'px';
}

// 隐藏相框右键菜单
function hideFrameContextMenu() {
    elements.frameContextMenu.style.display = 'none';
}

// 设置图片相框
function setImageFrame(frameName) {
    if (!appState.selectedImageForFrame) return;

    const imageIndex = appState.selectedImages.findIndex(img => img.id === appState.selectedImageForFrame.id);
    if (imageIndex !== -1) {
        appState.selectedImages[imageIndex].frame = frameName;
        renderSelectedGallery();

        const frameText = frameName ? `相框: ${frameName}` : '已移除相框';
        showToast(`${appState.selectedImageForFrame.name} ${frameText}`);
    }
}

// 显示管理密码验证模态框
function showAdminPasswordModal() {
    elements.adminPasswordModal.style.display = 'block';
    elements.adminPasswordInput.value = '';
    elements.adminPasswordInput.focus();
}

// 隐藏管理密码验证模态框
function hideAdminPasswordModal() {
    elements.adminPasswordModal.style.display = 'none';
    elements.adminPasswordInput.value = '';
}

// 验证管理密码
function verifyAdminPassword() {
    const password = elements.adminPasswordInput.value;
    const correctPassword = '147258369';

    if (password === correctPassword) {
        hideAdminPasswordModal();
        openAdminPanel();
    } else {
        showToast('密码错误，请重试', 'error');
        elements.adminPasswordInput.value = '';
        elements.adminPasswordInput.focus();
    }
}

// 打开管理后台
function openAdminPanel() {
    window.location.href = 'admin.html';
}

// 更新选中图片名字显示
function updateSelectedImageName(imageName) {
    if (imageName) {
        elements.selectedImageName.textContent = imageName;
    } else {
        elements.selectedImageName.textContent = '';
    }
}
