/* 管理后台样式 */
#admin-app {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 顶部导航 */
.admin-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.admin-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.admin-nav {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: #64748b;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.nav-btn.active {
    background: #3b82f6;
    color: white;
}

.back-button {
    padding: 0.5rem 1rem;
    background: #6b7280;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.back-button:hover {
    background: #4b5563;
}

/* 主内容区 */
.admin-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.tab-header h2 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: #1f2937;
}

.add-button {
    padding: 0.75rem 1.5rem;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-button:hover {
    background: #059669;
    transform: translateY(-1px);
}

.add-button::before {
    content: '+';
    font-size: 1.2rem;
    font-weight: bold;
}

/* 标签页描述 */
.tab-description {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.tab-description p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* 内容网格 */
.content-grid {
    display: grid;
    gap: 1.5rem;
}

.items-list {
    display: grid;
    gap: 1rem;
}

/* 项目卡片 */
.item-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.item-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

.edit-btn, .delete-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.edit-btn {
    background: #3b82f6;
    color: white;
}

.edit-btn:hover {
    background: #2563eb;
}

.delete-btn {
    background: #ef4444;
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
}

.item-content {
    color: #6b7280;
    line-height: 1.5;
}

.item-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #9ca3af;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 套餐特殊样式 */
.package-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
}

.package-count {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* 公告特殊样式 */
.announcement-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.announcement-type.promotion {
    background: #fef3c7;
    color: #92400e;
}

.announcement-type.benefit {
    background: #d1fae5;
    color: #065f46;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background: #10b981;
}

.status-indicator.inactive {
    background: #ef4444;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #9ca3af;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #6b7280;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-checkbox {
    margin-right: 0.5rem;
}

/* 按钮样式 */
.cancel-button, .save-button, .delete-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.cancel-button {
    background: #f3f4f6;
    color: #374151;
}

.cancel-button:hover {
    background: #e5e7eb;
}

.save-button {
    background: #3b82f6;
    color: white;
}

.save-button:hover {
    background: #2563eb;
}

.delete-button {
    background: #ef4444;
    color: white;
}

.delete-button:hover {
    background: #dc2626;
}

/* Toast 提示 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1100;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #ef4444;
}

.toast.warning {
    background: #f59e0b;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .admin-nav {
        justify-content: center;
    }

    .admin-content {
        padding: 1rem;
    }

    .tab-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
