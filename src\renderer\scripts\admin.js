// 检查是否在Electron环境中
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;
const ipcRenderer = isElectron ? require('electron').ipcRenderer : null;

// 应用状态
let adminState = {
    currentTab: 'packages',
    editingItem: null,
    editingType: null,
    data: {
        packages: [],
        frames: [],
        announcements: [],
        addon_services: []
    }
};

// DOM 元素
const elements = {
    navBtns: document.querySelectorAll('.nav-btn'),
    tabContents: document.querySelectorAll('.tab-content'),
    backBtn: document.getElementById('back-to-main'),
    editModal: document.getElementById('edit-modal'),
    confirmModal: document.getElementById('confirm-modal'),
    editForm: document.getElementById('edit-form'),
    modalTitle: document.getElementById('modal-title'),
    loading: document.getElementById('admin-loading'),
    toast: document.getElementById('admin-toast'),
    
    // 添加按钮
    addPackageBtn: document.getElementById('add-package-btn'),
    addFrameBtn: document.getElementById('add-frame-btn'),
    addAnnouncementBtn: document.getElementById('add-announcement-btn'),
    addAddonServiceBtn: document.getElementById('add-addon-service-btn'),
    
    // 列表容器
    packagesList: document.getElementById('packages-list'),
    framesList: document.getElementById('frames-list'),
    announcementsList: document.getElementById('announcements-list'),
    addonServicesList: document.getElementById('addon-services-list')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    await loadData();
    initializeEventListeners();
    renderCurrentTab();
});

// 加载数据
async function loadData() {
    try {
        showLoading(true);
        
        if (ipcRenderer) {
            // Electron环境，从主进程加载数据
            adminState.data.packages = await ipcRenderer.invoke('get-packages');
            adminState.data.frames = await ipcRenderer.invoke('get-frames');
            adminState.data.announcements = await ipcRenderer.invoke('get-announcements');
            adminState.data.addon_services = await ipcRenderer.invoke('get-addon-services');
        } else {
            // 浏览器环境，使用模拟数据
            const mockData = getMockData();
            adminState.data = mockData;
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        showToast('加载数据失败', 'error');
    } finally {
        showLoading(false);
    }
}

// 获取模拟数据
function getMockData() {
    return {
        packages: [
            { id: 1, name: '￥1099 套餐', price: 1099, refine_count: 30, description: '基础套餐，包含30张精修照片' },
            { id: 2, name: '￥1599 套餐', price: 1599, refine_count: 50, description: '标准套餐，包含50张精修照片' },
            { id: 3, name: '￥2099 套餐', price: 2099, refine_count: 80, description: '豪华套餐，包含80张精修照片' }
        ],
        frames: [
            { id: 1, name: '简约白框', description: '简洁的白色相框' },
            { id: 2, name: '复古金框', description: '复古风格的金色相框' },
            { id: 3, name: '现代黑框', description: '现代简约的黑色相框' },
            { id: 4, name: '木质相框', description: '天然木质相框' }
        ],
        announcements: [
            { id: 1, title: '好评活动', content: '五星好评送精修加油包！', type: 'promotion', is_active: true },
            { id: 2, title: '续订福利', content: '老客户续订享受9折优惠', type: 'benefit', is_active: true }
        ],
        addon_services: [
            { id: 1, name: '精修加油包', price: 20, unit: '张', description: '额外的精修照片服务', is_active: true }
        ]
    };
}

// 初始化事件监听器
function initializeEventListeners() {
    // 导航按钮
    elements.navBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tab = btn.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 返回主界面
    elements.backBtn.addEventListener('click', () => {
        window.location.href = 'index.html';
    });
    
    // 添加按钮
    elements.addPackageBtn.addEventListener('click', () => openEditModal('package'));
    elements.addFrameBtn.addEventListener('click', () => openEditModal('frame'));
    elements.addAnnouncementBtn.addEventListener('click', () => openEditModal('announcement'));
    elements.addAddonServiceBtn.addEventListener('click', () => openEditModal('addon-service'));
    
    // 模态框关闭
    document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    document.getElementById('cancel-edit').addEventListener('click', closeModal);
    document.getElementById('cancel-delete').addEventListener('click', closeModal);
    
    // 表单提交
    elements.editForm.addEventListener('submit', handleFormSubmit);
    
    // 确认删除
    document.getElementById('confirm-delete').addEventListener('click', handleConfirmDelete);
    
    // 点击模态框背景关闭
    elements.editModal.addEventListener('click', (e) => {
        if (e.target === elements.editModal) closeModal();
    });
    
    elements.confirmModal.addEventListener('click', (e) => {
        if (e.target === elements.confirmModal) closeModal();
    });
}

// 切换标签页
function switchTab(tab) {
    adminState.currentTab = tab;
    
    // 更新导航按钮状态
    elements.navBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tab);
    });
    
    // 更新标签页内容
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tab}-tab`);
    });
    
    renderCurrentTab();
}

// 渲染当前标签页
function renderCurrentTab() {
    switch (adminState.currentTab) {
        case 'packages':
            renderPackages();
            break;
        case 'frames':
            renderFrames();
            break;
        case 'announcements':
            renderAnnouncements();
            break;
        case 'addon-services':
            renderAddonServices();
            break;
    }
}

// 渲染套餐列表
function renderPackages() {
    const container = elements.packagesList;
    container.innerHTML = '';
    
    adminState.data.packages.forEach(pkg => {
        const card = createPackageCard(pkg);
        container.appendChild(card);
    });
}

// 创建套餐卡片
function createPackageCard(pkg) {
    const div = document.createElement('div');
    div.className = 'item-card';
    div.innerHTML = `
        <div class="item-header">
            <h3 class="item-title">${pkg.name}</h3>
            <div class="item-actions">
                <button class="edit-btn" onclick="editItem('package', ${pkg.id})">编辑</button>
                <button class="delete-btn" onclick="deleteItem('package', ${pkg.id})">删除</button>
            </div>
        </div>
        <div class="item-content">
            <div class="package-price">￥${pkg.price}</div>
            <p>${pkg.description}</p>
            <div class="item-meta">
                <div class="meta-item">
                    <span class="package-count">${pkg.refine_count}张精修</span>
                </div>
            </div>
        </div>
    `;
    return div;
}

// 渲染相框列表
function renderFrames() {
    const container = elements.framesList;
    container.innerHTML = '';
    
    adminState.data.frames.forEach(frame => {
        const card = createFrameCard(frame);
        container.appendChild(card);
    });
}

// 创建相框卡片
function createFrameCard(frame) {
    const div = document.createElement('div');
    div.className = 'item-card';
    div.innerHTML = `
        <div class="item-header">
            <h3 class="item-title">${frame.name}</h3>
            <div class="item-actions">
                <button class="edit-btn" onclick="editItem('frame', ${frame.id})">编辑</button>
                <button class="delete-btn" onclick="deleteItem('frame', ${frame.id})">删除</button>
            </div>
        </div>
        <div class="item-content">
            <p>${frame.description}</p>
        </div>
    `;
    return div;
}

// 渲染公告列表
function renderAnnouncements() {
    const container = elements.announcementsList;
    container.innerHTML = '';
    
    adminState.data.announcements.forEach(announcement => {
        const card = createAnnouncementCard(announcement);
        container.appendChild(card);
    });
}

// 创建公告卡片
function createAnnouncementCard(announcement) {
    const div = document.createElement('div');
    div.className = 'item-card';
    div.innerHTML = `
        <div class="item-header">
            <h3 class="item-title">${announcement.title}</h3>
            <div class="item-actions">
                <button class="edit-btn" onclick="editItem('announcement', ${announcement.id})">编辑</button>
                <button class="delete-btn" onclick="deleteItem('announcement', ${announcement.id})">删除</button>
            </div>
        </div>
        <div class="item-content">
            <p>${announcement.content}</p>
            <div class="item-meta">
                <div class="meta-item">
                    <span class="announcement-type ${announcement.type}">${getAnnouncementTypeText(announcement.type)}</span>
                </div>
                <div class="meta-item">
                    <span class="status-indicator ${announcement.is_active ? 'active' : 'inactive'}"></span>
                    ${announcement.is_active ? '启用' : '禁用'}
                </div>
            </div>
        </div>
    `;
    return div;
}

// 渲染增值服务列表
function renderAddonServices() {
    const container = elements.addonServicesList;
    container.innerHTML = '';
    
    adminState.data.addon_services.forEach(service => {
        const card = createAddonServiceCard(service);
        container.appendChild(card);
    });
}

// 创建增值服务卡片
function createAddonServiceCard(service) {
    const div = document.createElement('div');
    div.className = 'item-card';
    div.innerHTML = `
        <div class="item-header">
            <h3 class="item-title">${service.name}</h3>
            <div class="item-actions">
                <button class="edit-btn" onclick="editItem('addon-service', ${service.id})">编辑</button>
                <button class="delete-btn" onclick="deleteItem('addon-service', ${service.id})">删除</button>
            </div>
        </div>
        <div class="item-content">
            <div class="package-price">￥${service.price}/${service.unit}</div>
            <p>${service.description}</p>
            <div class="item-meta">
                <div class="meta-item">
                    <span class="status-indicator ${service.is_active ? 'active' : 'inactive'}"></span>
                    ${service.is_active ? '启用' : '禁用'}
                </div>
            </div>
        </div>
    `;
    return div;
}

// 获取公告类型文本
function getAnnouncementTypeText(type) {
    const types = {
        'promotion': '促销活动',
        'benefit': '福利优惠',
        'notice': '重要通知'
    };
    return types[type] || type;
}

// 显示/隐藏加载状态
function showLoading(show) {
    elements.loading.style.display = show ? 'flex' : 'none';
}

// 显示Toast提示
function showToast(message, type = 'success') {
    elements.toast.textContent = message;
    elements.toast.className = `toast ${type} show`;
    
    setTimeout(() => {
        elements.toast.classList.remove('show');
    }, 3000);
}

// 关闭模态框
function closeModal() {
    elements.editModal.classList.remove('show');
    elements.confirmModal.classList.remove('show');
    adminState.editingItem = null;
    adminState.editingType = null;
}

// 打开编辑模态框
function openEditModal(type, item = null) {
    adminState.editingType = type;
    adminState.editingItem = item;

    const isEdit = item !== null;
    elements.modalTitle.textContent = isEdit ? `编辑${getTypeText(type)}` : `添加${getTypeText(type)}`;

    // 生成表单
    generateForm(type, item);

    elements.editModal.classList.add('show');
}

// 编辑项目
function editItem(type, id) {
    const data = adminState.data[getDataKey(type)];
    const item = data.find(item => item.id === id);
    if (item) {
        openEditModal(type, item);
    }
}

// 删除项目
function deleteItem(type, id) {
    adminState.editingType = type;
    adminState.editingItem = { id };

    const typeText = getTypeText(type);
    document.getElementById('confirm-message').textContent = `确定要删除这个${typeText}吗？此操作不可撤销。`;

    elements.confirmModal.classList.add('show');
}

// 生成表单
function generateForm(type, item) {
    const form = elements.editForm;
    form.innerHTML = '';

    switch (type) {
        case 'package':
            form.innerHTML = `
                <div class="form-group">
                    <label class="form-label">套餐名称</label>
                    <input type="text" name="name" class="form-input" value="${item?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">价格（元）</label>
                    <input type="number" name="price" class="form-input" value="${item?.price || ''}" required min="0">
                </div>
                <div class="form-group">
                    <label class="form-label">精修张数</label>
                    <input type="number" name="refine_count" class="form-input" value="${item?.refine_count || ''}" required min="1">
                </div>
                <div class="form-group">
                    <label class="form-label">套餐描述</label>
                    <textarea name="description" class="form-textarea" placeholder="请输入套餐描述">${item?.description || ''}</textarea>
                </div>
            `;
            break;

        case 'frame':
            form.innerHTML = `
                <div class="form-group">
                    <label class="form-label">相框名称</label>
                    <input type="text" name="name" class="form-input" value="${item?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">相框描述</label>
                    <textarea name="description" class="form-textarea" placeholder="请输入相框描述">${item?.description || ''}</textarea>
                </div>
            `;
            break;

        case 'announcement':
            form.innerHTML = `
                <div class="form-group">
                    <label class="form-label">公告标题</label>
                    <input type="text" name="title" class="form-input" value="${item?.title || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">公告内容</label>
                    <textarea name="content" class="form-textarea" placeholder="请输入公告内容" required>${item?.content || ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">公告类型</label>
                    <select name="type" class="form-select" required>
                        <option value="promotion" ${item?.type === 'promotion' ? 'selected' : ''}>促销活动</option>
                        <option value="benefit" ${item?.type === 'benefit' ? 'selected' : ''}>福利优惠</option>
                        <option value="notice" ${item?.type === 'notice' ? 'selected' : ''}>重要通知</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" name="is_active" class="form-checkbox" ${item?.is_active !== false ? 'checked' : ''}>
                        启用此公告
                    </label>
                </div>
            `;
            break;

        case 'addon-service':
            form.innerHTML = `
                <div class="form-group">
                    <label class="form-label">服务名称</label>
                    <input type="text" name="name" class="form-input" value="${item?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">价格（元）</label>
                    <input type="number" name="price" class="form-input" value="${item?.price || ''}" required min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label class="form-label">计价单位</label>
                    <input type="text" name="unit" class="form-input" value="${item?.unit || '张'}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">服务描述</label>
                    <textarea name="description" class="form-textarea" placeholder="请输入服务描述">${item?.description || ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" name="is_active" class="form-checkbox" ${item?.is_active !== false ? 'checked' : ''}>
                        启用此服务
                    </label>
                </div>
            `;
            break;
    }
}

// 处理表单提交
async function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {};

    // 收集表单数据
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active') {
            data[key] = true;
        } else if (key === 'price' || key === 'refine_count') {
            data[key] = parseFloat(value);
        } else {
            data[key] = value;
        }
    }

    // 如果没有勾选is_active复选框，设置为false
    if (!formData.has('is_active') && (adminState.editingType === 'announcement' || adminState.editingType === 'addon-service')) {
        data.is_active = false;
    }

    try {
        showLoading(true);

        if (adminState.editingItem && adminState.editingItem.id) {
            // 编辑现有项目
            await updateItem(adminState.editingType, adminState.editingItem.id, data);
        } else {
            // 创建新项目
            await createItem(adminState.editingType, data);
        }

        closeModal();
        await loadData();
        renderCurrentTab();
        showToast('保存成功');

    } catch (error) {
        console.error('保存失败:', error);
        showToast('保存失败', 'error');
    } finally {
        showLoading(false);
    }
}

// 处理确认删除
async function handleConfirmDelete() {
    if (!adminState.editingItem || !adminState.editingItem.id) return;

    try {
        showLoading(true);

        await deleteItemById(adminState.editingType, adminState.editingItem.id);

        closeModal();
        await loadData();
        renderCurrentTab();
        showToast('删除成功');

    } catch (error) {
        console.error('删除失败:', error);
        showToast('删除失败', 'error');
    } finally {
        showLoading(false);
    }
}

// 创建项目
async function createItem(type, data) {
    if (ipcRenderer) {
        return await ipcRenderer.invoke(`create-${type.replace('-', '_')}`, data);
    } else {
        // 浏览器环境模拟
        const dataKey = getDataKey(type);
        const newId = Math.max(...adminState.data[dataKey].map(item => item.id), 0) + 1;
        const newItem = { id: newId, ...data };
        adminState.data[dataKey].push(newItem);
        return { id: newId };
    }
}

// 更新项目
async function updateItem(type, id, data) {
    if (ipcRenderer) {
        return await ipcRenderer.invoke(`update-${type.replace('-', '_')}`, id, data);
    } else {
        // 浏览器环境模拟
        const dataKey = getDataKey(type);
        const index = adminState.data[dataKey].findIndex(item => item.id === id);
        if (index !== -1) {
            adminState.data[dataKey][index] = { ...adminState.data[dataKey][index], ...data };
        }
        return { changes: 1 };
    }
}

// 删除项目
async function deleteItemById(type, id) {
    if (ipcRenderer) {
        return await ipcRenderer.invoke(`delete-${type.replace('-', '_')}`, id);
    } else {
        // 浏览器环境模拟
        const dataKey = getDataKey(type);
        const index = adminState.data[dataKey].findIndex(item => item.id === id);
        if (index !== -1) {
            adminState.data[dataKey].splice(index, 1);
        }
        return { changes: 1 };
    }
}

// 获取类型文本
function getTypeText(type) {
    const types = {
        'package': '套餐',
        'frame': '相框',
        'announcement': '公告',
        'addon-service': '增值服务'
    };
    return types[type] || type;
}

// 获取数据键名
function getDataKey(type) {
    const keys = {
        'package': 'packages',
        'frame': 'frames',
        'announcement': 'announcements',
        'addon-service': 'addon_services'
    };
    return keys[type] || type;
}

// 全局函数，供HTML调用
window.editItem = editItem;
window.deleteItem = deleteItem;
