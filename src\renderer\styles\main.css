/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
    overflow: hidden;
}

/* 主容器 */
.main-container {
    display: flex;
    height: 100vh;
    gap: 1px;
    background-color: #e9ecef;
}

/* 左侧边栏 */
.sidebar {
    width: 280px;
    background-color: white;
    padding: 20px;
    overflow-y: auto;
    border-radius: 0 8px 8px 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.sidebar-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f3f4;
}

.sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 12px;
}

/* 产品选择 */
.select-package-button {
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.select-package-button:hover {
    background-color: #0056b3;
}

.selected-package-info {
    text-align: center;
}

.selected-package-name {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 8px;
    font-size: 14px;
}

.change-package-button {
    padding: 6px 12px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.change-package-button:hover {
    background-color: #545b62;
}

/* 套餐内容 */
.package-content {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.package-content p {
    font-size: 14px;
    color: #6c757d;
}

/* 公告区域 */
.announcement-item {
    background-color: #fff3cd;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    border-left: 3px solid #ffc107;
}

.announcement-item h4 {
    font-size: 13px;
    font-weight: 600;
    color: #856404;
    margin-bottom: 4px;
}

.announcement-item p {
    font-size: 12px;
    color: #856404;
}

/* 增值服务 */
.addon-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.addon-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
}

/* 相框选择 */
.frame-selection select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background-color: white;
    font-size: 14px;
}

.frame-selection select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
}

.frame-hint {
    font-size: 12px;
    color: #6c757d;
    margin-top: 6px;
}

/* 确认按钮 */
.confirm-button {
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.confirm-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.confirm-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 帮助按钮 */
.help-button {
    width: 100%;
    padding: 8px 16px;
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.help-button:hover {
    background-color: #f8f9fa;
    color: #495057;
    border-color: #adb5bd;
}

/* 管理后台按钮 */
.admin-button {
    width: 100%;
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.admin-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 中间画廊区域 */
.gallery-section {
    flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    margin: 0 1px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #f1f3f4;
}

.gallery-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
}

.import-button {
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.import-button:hover {
    background-color: #218838;
}

.gallery-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    align-content: start;
}

/* 右侧选择区域 */
.selected-section {
    width: 320px;
    background-color: white;
    display: flex;
    flex-direction: column;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
}

.selected-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f1f3f4;
}

.selected-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
}

.selected-image-name {
    margin-top: 8px;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    color: #6c757d;
    border: 1px solid #e9ecef;
    min-height: 28px;
    display: flex;
    align-items: center;
}

.selected-image-name:empty::before {
    content: "选择照片后显示文件名";
    color: #adb5bd;
    font-style: italic;
}

/* 操作提示样式 */
.gallery-tips, .selected-tips {
    padding: 12px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
}

.tip-icon {
    font-size: 14px;
}

.tip-text {
    font-weight: 500;
}

.selected-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    align-content: start;
}

/* 图片缩略图 */
.image-thumbnail {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    border: 2px solid transparent;
}

.image-thumbnail:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.image-thumbnail:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.1);
    z-index: 1;
    pointer-events: none;
}

.image-thumbnail[data-type="original"]:hover::after {
    content: '双击添加到精修';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 2;
    pointer-events: none;
}

.image-thumbnail[data-type="selected"]:hover::after {
    content: '双击移除精修';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 2;
    pointer-events: none;
}

.image-thumbnail.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.image-thumbnail.in-selected {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.image-thumbnail.in-selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #28a745;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.image-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 8px 6px 4px;
    font-size: 11px;
    text-align: center;
}

.frame-info {
    font-size: 10px;
    color: #ffc107;
    margin-top: 2px;
}

/* 空状态 */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state p {
    font-size: 16px;
}

/* PhotoSwipe 自定义样式 */
.pswp__caption__center {
    text-align: center;
    max-width: 420px;
    margin: 0 auto;
    font-size: 14px;
    line-height: 1.4;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px 15px;
    border-radius: 8px;
}

.pswp__button--share {
    display: none !important;
}

/* 加载提示 */
.loading {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    font-size: 16px;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.toast-error {
    background-color: #dc3545;
}

.toast.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast.toast-info {
    background-color: #17a2b8;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.toast-error {
    background-color: #dc3545;
}

.toast.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast.toast-info {
    background-color: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .sidebar {
        width: 100%;
        border-radius: 8px 8px 0 0;
        max-height: 300px;
        overflow-y: auto;
    }

    .gallery-section {
        border-radius: 0;
        margin: 1px 0;
    }

    .selected-section {
        width: 100%;
        border-radius: 0 0 8px 8px;
    }

    .gallery-container {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .selected-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-container {
        gap: 0;
    }

    .sidebar {
        padding: 15px;
        border-radius: 0;
    }

    .gallery-section,
    .selected-section {
        border-radius: 0;
    }

    .gallery-header {
        padding: 15px 20px;
    }

    .selected-header {
        padding: 15px 20px;
    }

    .gallery-container {
        padding: 15px;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 12px;
    }

    .selected-container {
        padding: 15px;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 10px;
    }


}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.image-thumbnail {
    animation: fadeIn 0.3s ease-out;
}

.sidebar {
    animation: slideIn 0.3s ease-out;
}

/* 改进的滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
}

/* 改进的焦点样式 */
input:focus,
select:focus,
button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* 改进的禁用状态 */
input:disabled,
select:disabled,
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 加载状态改进 */
.loading {
    backdrop-filter: blur(4px);
}

.loading-spinner {
    border-width: 3px;
    border-top-color: #007bff;
}

/* 套餐内容样式改进 */
.package-content {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.package-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #007bff, #0056b3);
}

/* 图片信息样式改进 */
.image-info {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    backdrop-filter: blur(2px);
}

/* 套餐选择模态框 */
.package-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.package-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.package-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.package-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
}

.package-modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;
}

.package-modal-close:hover {
    color: #dc3545;
}

.package-modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.package-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.package-option-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: white;
}

.package-option-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.package-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.package-card-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.package-price {
    font-size: 18px;
    font-weight: 700;
    color: #007bff;
}

.package-card-content {
    margin-bottom: 16px;
}

.package-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
}

.package-details {
    display: flex;
    align-items: center;
    gap: 12px;
}

.package-count {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.select-package-card-btn {
    width: 100%;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.select-package-card-btn:hover {
    background-color: #0056b3;
}

/* 右键菜单 */
.context-menu {
    display: none;
    position: fixed;
    z-index: 1001;
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 150px;
    overflow: hidden;
}

.context-menu-header {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
}

.context-menu-options {
    padding: 4px 0;
}

.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s ease;
}

.context-menu-item:hover {
    background-color: #f8f9fa;
}

.context-menu-item:active {
    background-color: #e9ecef;
}

/* 管理密码验证模态框 */
.admin-password-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.admin-password-content {
    background-color: white;
    margin: 15% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.admin-password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.admin-password-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.admin-password-close {
    font-size: 20px;
    font-weight: bold;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;
}

.admin-password-close:hover {
    color: #dc3545;
}

.admin-password-body {
    padding: 24px;
}

.admin-password-body .form-group {
    margin-bottom: 20px;
}

.admin-password-body label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.admin-password-body .form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.admin-password-body .form-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.admin-password-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.admin-password-actions .cancel-button {
    padding: 8px 16px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.admin-password-actions .cancel-button:hover {
    background-color: #545b62;
}

.admin-password-actions .confirm-button {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.admin-password-actions .confirm-button:hover {
    background-color: #0056b3;
}

/* 按钮悬停效果改进 */
.import-button,
.confirm-button {
    position: relative;
    overflow: hidden;
}

.import-button::before,
.confirm-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.import-button:hover::before,
.confirm-button:hover:not(:disabled)::before {
    left: 100%;
}
